'use client'

import { useState, useRef, useEffect } from 'react'

interface PriceRange {
  value: string
  label: string
  min?: number
  max?: number
  popular?: boolean
}

interface PriceRangeFilterProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  currency?: string
  listingType?: 'sale' | 'rent'
}

const PriceRangeFilter = ({ 
  value, 
  onChange, 
  placeholder = "Price Range", 
  className = "",
  currency = "₱",
  listingType = 'sale'
}: PriceRangeFilterProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [customMin, setCustomMin] = useState('')
  const [customMax, setCustomMax] = useState('')
  const [showCustom, setShowCustom] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // Price ranges for sale properties
  const salePriceRanges: PriceRange[] = [
    { value: '', label: 'Any Price', popular: true },
    { value: '0-1000000', label: 'Under ₱1M', min: 0, max: 1000000, popular: true },
    { value: '1000000-3000000', label: '₱1M - ₱3M', min: 1000000, max: 3000000, popular: true },
    { value: '3000000-5000000', label: '₱3M - ₱5M', min: 3000000, max: 5000000, popular: true },
    { value: '5000000-10000000', label: '₱5M - ₱10M', min: 5000000, max: 10000000, popular: true },
    { value: '10000000-20000000', label: '₱10M - ₱20M', min: 10000000, max: 20000000 },
    { value: '20000000-50000000', label: '₱20M - ₱50M', min: 20000000, max: 50000000 },
    { value: '50000000+', label: '₱50M+', min: 50000000, popular: true }
  ]

  // Price ranges for rent properties
  const rentPriceRanges: PriceRange[] = [
    { value: '', label: 'Any Price', popular: true },
    { value: '0-15000', label: 'Under ₱15K/month', min: 0, max: 15000, popular: true },
    { value: '15000-30000', label: '₱15K - ₱30K/month', min: 15000, max: 30000, popular: true },
    { value: '30000-50000', label: '₱30K - ₱50K/month', min: 30000, max: 50000, popular: true },
    { value: '50000-100000', label: '₱50K - ₱100K/month', min: 50000, max: 100000, popular: true },
    { value: '100000-200000', label: '₱100K - ₱200K/month', min: 100000, max: 200000 },
    { value: '200000-500000', label: '₱200K - ₱500K/month', min: 200000, max: 500000 },
    { value: '500000+', label: '₱500K+/month', min: 500000 }
  ]

  const priceRanges = listingType === 'rent' ? rentPriceRanges : salePriceRanges
  const selectedRange = priceRanges.find(range => range.value === value) || priceRanges[0]

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
        setShowCustom(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(num % 1000000 === 0 ? 0 : 1)}M`
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(num % 1000 === 0 ? 0 : 1)}K`
    }
    return num.toLocaleString()
  }

  const handleSelect = (range: PriceRange) => {
    onChange(range.value)
    setIsOpen(false)
    setShowCustom(false)
  }

  const handleCustomRange = () => {
    const min = parseInt(customMin.replace(/,/g, '')) || 0
    const max = parseInt(customMax.replace(/,/g, '')) || 0
    
    if (min > 0 || max > 0) {
      const customValue = max > 0 ? `${min}-${max}` : `${min}+`
      const customLabel = max > 0 
        ? `${currency}${formatNumber(min)} - ${currency}${formatNumber(max)}${listingType === 'rent' ? '/month' : ''}`
        : `${currency}${formatNumber(min)}+${listingType === 'rent' ? '/month' : ''}`
      
      onChange(customValue)
      setIsOpen(false)
      setShowCustom(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false)
      setShowCustom(false)
      buttonRef.current?.blur()
    } else if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      setIsOpen(!isOpen)
    }
  }

  return (
    <div className={`relative ${className}`}>
      <button
        ref={buttonRef}
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 bg-white text-left flex items-center justify-between"
      >
        <div className="flex items-center">
          <svg className="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
          <span className={selectedRange.value ? 'text-gray-900' : 'text-gray-500'}>
            {selectedRange.label || placeholder}
          </span>
        </div>
        <svg
          className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto"
        >
          {/* Popular ranges */}
          <div className="p-2">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide px-2 py-1">
              Popular Ranges
            </div>
            {priceRanges.filter(range => range.popular).map((range) => (
              <button
                key={range.value || 'any'}
                onClick={() => handleSelect(range)}
                className={`w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors duration-150 rounded ${
                  range.value === value ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium">{range.label}</span>
                  {range.value === value && (
                    <svg className="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </button>
            ))}
          </div>

          {/* All ranges */}
          <div className="border-t border-gray-100 p-2">
            <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide px-2 py-1">
              All Ranges
            </div>
            {priceRanges.filter(range => !range.popular).map((range) => (
              <button
                key={range.value || 'any'}
                onClick={() => handleSelect(range)}
                className={`w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors duration-150 rounded ${
                  range.value === value ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span>{range.label}</span>
                  {range.value === value && (
                    <svg className="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </button>
            ))}
          </div>

          {/* Custom range */}
          <div className="border-t border-gray-100 p-3">
            <button
              onClick={() => setShowCustom(!showCustom)}
              className="w-full text-left text-blue-600 hover:text-blue-700 font-medium"
            >
              Custom Range
            </button>
            
            {showCustom && (
              <div className="mt-3 space-y-3">
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="text"
                    placeholder="Min price"
                    value={customMin}
                    onChange={(e) => setCustomMin(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded text-sm focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="text"
                    placeholder="Max price"
                    value={customMax}
                    onChange={(e) => setCustomMax(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded text-sm focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <button
                  onClick={handleCustomRange}
                  className="w-full bg-blue-600 text-white py-2 rounded text-sm hover:bg-blue-700 transition-colors"
                >
                  Apply Custom Range
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default PriceRangeFilter
