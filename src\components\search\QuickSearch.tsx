'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

interface QuickSearchProps {
  placeholder?: string
  className?: string
  onSearch?: (query: string) => void
}

const QuickSearch = ({ 
  placeholder = "Search properties...", 
  className = "",
  onSearch 
}: QuickSearchProps) => {
  const [query, setQuery] = useState('')
  const [isExpanded, setIsExpanded] = useState(false)
  const router = useRouter()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (query.trim()) {
      if (onSearch) {
        onSearch(query.trim())
      } else {
        // Default behavior: navigate to properties page with search query
        router.push(`/properties?search=${encodeURIComponent(query.trim())}`)
      }
      setQuery('')
      setIsExpanded(false)
    }
  }

  const handleFocus = () => {
    setIsExpanded(true)
  }

  const handleBlur = () => {
    // Delay collapse to allow for form submission
    setTimeout(() => {
      if (!query) {
        setIsExpanded(false)
      }
    }, 150)
  }

  return (
    <div className={`relative ${className}`}>
      <form onSubmit={handleSearch} className="relative">
        <div className={`
          flex items-center transition-all duration-300 ease-in-out
          ${isExpanded 
            ? 'w-80 bg-white border border-gray-300 rounded-lg shadow-lg' 
            : 'w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-lg cursor-pointer'
          }
        `}>
          {isExpanded ? (
            <>
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onFocus={handleFocus}
                onBlur={handleBlur}
                placeholder={placeholder}
                className="flex-1 px-4 py-3 bg-transparent border-none outline-none text-gray-900 placeholder-gray-500"
                autoFocus
              />
              <button
                type="submit"
                className="px-4 py-3 text-gray-400 hover:text-blue-600 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </>
          ) : (
            <button
              type="button"
              onClick={handleFocus}
              className="w-full h-full flex items-center justify-center text-gray-600 hover:text-blue-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          )}
        </div>
      </form>

      {/* Search suggestions could be added here */}
      {isExpanded && query.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
          <div className="p-2">
            <div className="px-3 py-2 text-sm text-gray-500">
              Press Enter to search for "{query}"
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default QuickSearch
